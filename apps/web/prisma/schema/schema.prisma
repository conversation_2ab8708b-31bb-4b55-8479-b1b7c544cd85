// Prisma 6.9.0 Schema for BuddyChip
// Modern PostgreSQL schema with advanced indexing and optimizations

generator client {
  provider      = "prisma-client-js"
  output        = "../generated"
  binaryTargets = ["native", "rhel-openssl-3.0.x", "linux-musl", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// Core User model linked to Clerk authentication
model User {
  id                 String             @id // Clerk user ID (no @default needed)
  email              String?            @unique
  name               String?
  avatar             String?            // Profile picture URL
  isAdmin            <PERSON>an            @default(false)
  
  // Subscription relationship (legacy - keeping for migration)
  plan               SubscriptionPlan   @relation(fields: [planId], references: [id], onDelete: Restrict)
  planId             String

  // Clerk Billing fields
  clerkPlanId        String?            // Clerk plan ID from billing
  clerkPlanName      String?            // Clerk plan name (e.g., "reply-guy", "reply-god")
  subscriptionStatus String?            // active, inactive, canceled, etc.
  subscriptionUpdatedAt DateTime?        // Last billing update

  // Personality settings
  selectedPersonality PersonalityProfile? @relation(fields: [personalityId], references: [id], onDelete: SetNull)
  personalityId       String?
  customSystemPrompt  String?            // User's custom system prompt
  useFirstPerson      Boolean            @default(true) // Whether AI responds as account owner (1st person) or external user (3rd person)

  // AI Model settings
  selectedModel       AIModel?           @relation(fields: [modelId], references: [id], onDelete: SetNull)
  modelId             String?            // Selected AI model

  // One-to-many relationships
  monitoredAccounts  MonitoredAccount[]
  aiResponses        AIResponse[]
  mentions           Mention[]          // For tracking user-created mentions
  images             Image[]            // User-uploaded images
  usageLogs          UsageLog[]         // Rate limiting tracking
  memories           Memory[]           // AI memory storage for personalized responses
  telegramUsers      TelegramUser[]     // Telegram bot integration
  mentionNotepads    MentionNotepad[]   // Enhanced notepad sessions
  createdPersonalities PersonalityProfile[] @relation("CreatedPersonalities") // User-generated personas
  personaGenerationJobs PersonaGenerationJob[] // Persona generation tracking
  
  // Timestamps
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @updatedAt
  lastActiveAt       DateTime?          // Track user activity

  // Indexes for performance
  @@index([planId])
  @@index([email])
  @@index([createdAt])
  @@index([lastActiveAt])
  @@index([personalityId])
  @@index([modelId])
  @@map("users")
}

// Personality profiles for AI responses
model PersonalityProfile {
  id          String   @id @default(cuid())
  name        String   @unique // e.g. "Tech Bro", "Crypto Degen"
  description String   // Brief description of the personality
  systemPrompt String  // The system prompt template for this personality
  isDefault   Boolean  @default(false) // Whether this is the default personality
  isActive    Boolean  @default(true)  // Whether this personality is available

  // User-generated persona fields
  isUserGenerated     Boolean  @default(false) // Whether this was generated from Twitter data
  sourceTwitterHandle String?  // Twitter handle used for generation
  generationMetadata  Json?    // Analysis details, tweet count, generation date, etc.
  createdBy           User?    @relation("CreatedPersonalities", fields: [createdById], references: [id], onDelete: Cascade)
  createdById         String?  // User who generated this persona

  // Relationships
  users       User[]
  generationJobs PersonaGenerationJob[] // Jobs that generated this personality

  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Indexes
  @@index([name])
  @@index([isActive])
  @@index([isDefault])
  @@index([isUserGenerated])
  @@index([createdById])
  @@index([sourceTwitterHandle])
  @@map("personality_profiles")
}

// Persona generation job tracking
model PersonaGenerationJob {
  id                    String   @id @default(cuid())

  // User and target information
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId                String
  twitterHandle         String   // Twitter handle to analyze

  // Job status and progress
  status                String   @default("pending") // 'pending', 'fetching_tweets', 'fetching_replies', 'analyzing', 'generating', 'storing_memories', 'completed', 'failed'
  progress              Int      @default(0) // 0-100 percentage

  // Collection metrics
  tweetsCollected       Int      @default(0)
  repliesCollected      Int      @default(0)
  totalTweetsTarget     Int      @default(500)
  totalRepliesTarget    Int      @default(500)

  // Memory storage tracking
  memoriesStored        Int      @default(0)
  memoryStorageProgress Int      @default(0) // 0-100 percentage for memory storage phase
  memoryErrors          Json?    // Any memory storage errors

  // Results and errors
  errorMessage          String?
  resultPersonalityId   String?  // ID of generated PersonalityProfile
  resultPersonality     PersonalityProfile? @relation(fields: [resultPersonalityId], references: [id], onDelete: SetNull)

  // Processing metadata
  processingMetadata    Json?    // Store intermediate analysis results

  // Timestamps
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  completedAt           DateTime?

  // Indexes
  @@index([userId])
  @@index([status])
  @@index([createdAt(sort: Desc)])
  @@map("persona_generation_jobs")
}

// AI Model configurations for different capabilities
model AIModel {
  id          String   @id @default(cuid())
  name        String   @unique // "Workhorse", "Smarty", "Big Brain"
  displayName String   // User-friendly display name
  description String   // Description of model capabilities
  provider    String   // "openrouter" or "openai"
  modelId     String   // API model identifier (e.g. "google/gemini-2.5-flash")
  costTier    String   // "low", "medium", "high"
  speed       String   // "fast", "medium", "slow"
  isActive    Boolean  @default(true)
  
  // Relationships
  users       User[]   // Users who have selected this model
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Indexes
  @@index([name])
  @@index([isActive])
  @@index([provider])
  @@index([costTier])
  @@map("ai_models")
}

// Subscription plan model with flexible feature configuration
model SubscriptionPlan {
  id                  String              @id @default(cuid())
  name                String              @unique // e.g. "Reply Guy", "Reply God", "Team"
  displayName         String              // User-friendly name
  description         String?             // Plan description
  price               Decimal             @db.Decimal(10,2) // Monthly price in USD
  baseUsers           Int                 @default(1)
  additionalUserPrice Decimal?            @db.Decimal(10,2) // Price per extra user
  isActive            Boolean             @default(true)
  
  // Relationships
  features            PlanFeature[]
  users               User[]
  
  // Timestamps
  createdAt           DateTime            @default(now())
  updatedAt           DateTime            @updatedAt

  // Indexes
  @@index([name])
  @@index([isActive])
  @@map("subscription_plans")
}

// Feature limits per subscription plan
model PlanFeature {
  id        String           @id @default(cuid())
  plan      SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)
  planId    String
  feature   FeatureType      // Enum for type safety
  limit     Int              // Numeric limit per billing cycle (-1 for unlimited)
  
  // Timestamps
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // Compound unique constraint
  @@unique([planId, feature])
  @@index([planId])
  @@map("plan_features")
}

// Enum for feature types to ensure consistency
enum FeatureType {
  AI_CALLS
  IMAGE_GENERATIONS
  MONITORED_ACCOUNTS
  MENTIONS_PER_MONTH
  MENTIONS_PER_SYNC      // How many mentions to fetch per sync operation
  MAX_TOTAL_MENTIONS     // Maximum total unreplied mentions per account
  STORAGE_GB
  TEAM_MEMBERS
  COOKIE_API_CALLS       // Cookie.fun API calls for crypto data
  PERSONA_GENERATIONS    // AI-generated personas from Twitter data per month
  PERSONA_MEMORY_OPS     // Memory operations for persona data storage

  @@map("feature_type")
}

// Twitter accounts being monitored
model MonitoredAccount {
  id             String    @id @default(cuid())
  twitterHandle  String    // e.g. "elonmusk" (without @)
  twitterId      String?   // Twitter numeric ID for API calls
  displayName    String?   // Twitter display name
  avatarUrl      String?   // Twitter profile picture
  sector         String?   // Crypto sector (defi, gaming, infrastructure, etc.)
  isActive       Boolean   @default(true)
  
  // Ownership
  user           User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId         String
  
  // Relationships
  mentions       Mention[]
  
  // Content type sync preferences
  syncMentions   Boolean   @default(true)  // Sync tweets mentioning this account
  syncUserTweets Boolean   @default(false) // Sync tweets BY this account
  syncReplies    Boolean   @default(false) // Include replies (when syncUserTweets=true)
  syncRetweets   Boolean   @default(true)  // Include retweets (when syncUserTweets=true)
  
  // Monitoring metadata
  lastCheckedAt  DateTime? // Last time we checked for mentions
  totalMentions  Int       @default(0) // Cache mention count
  
  // Timestamps
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  // Indexes for performance
  @@unique([userId, twitterHandle]) // One user can't monitor same account twice
  @@index([userId])
  @@index([twitterId])
  @@index([isActive])
  @@index([lastCheckedAt])
  @@index([twitterHandle])
  @@index([sector])
  @@index([userId, sector]) // Compound index for filtering user's accounts by sector
  @@map("monitored_accounts")
}

// Tweet mentions with enhanced metadata
model Mention {
  id               String           @id // Tweet ID from Twitter API
  content          String           // Tweet text content
  link             String           @unique // Full URL to tweet
  
  // Author information
  authorName       String           // Display name
  authorHandle     String           // @username
  authorId         String?          // Twitter user ID
  authorAvatarUrl  String?          // Profile picture
  authorVerified   Boolean?         // Verification status
  
  // Tweet metadata
  mentionedAt      DateTime         // Original tweet timestamp
  replyCount       Int?             @default(0)
  retweetCount     Int?             @default(0)
  likeCount        Int?             @default(0)
  isReply          Boolean          @default(false)
  parentTweetId    String?          // If this is a reply
  
  // AI Analysis
  bullishScore     Int?             // 1-100 sentiment score
  importanceScore  Int?             // 1-100 importance/priority score
  analysisData     Json?            // Additional AI analysis results
  keywords         String[]         // Extracted keywords for search
  
  // Relationships
  account          MonitoredAccount? @relation(fields: [accountId], references: [id], onDelete: SetNull)
  accountId        String?          // Null for quick replies
  responses        AIResponse[]
  images           Image[]          // Attached images
  user             User?            @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId           String?          // User who created this mention
  notepad          MentionNotepad?  // Enhanced notepad for response crafting
  
  // Processing status
  processed        Boolean          @default(false)
  processingError  String?          // Store any processing errors
  
  // Archive status
  archived         Boolean          @default(false)   // Whether mention is archived
  archivedAt       DateTime?                          // When mention was archived
  
  // Tweet type flag
  isUserTweet      Boolean          @default(false)   // True if this is a user's own tweet, not a mention
  
  // Timestamps
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt

  // Advanced indexes for performance
  @@index([accountId])
  @@index([userId])
  @@index([mentionedAt(sort: Desc)]) // Most recent first
  @@index([bullishScore(sort: Desc)]) // Highest scores first
  @@index([importanceScore(sort: Desc)]) // Highest importance first
  @@index([processed])
  @@index([archived])              // Archive status queries
  @@index([authorHandle])
  @@index([createdAt(sort: Desc)])
  @@index([content])
  @@index([keywords])
  // Compound indexes for common query patterns
  @@index([accountId, mentionedAt(sort: Desc)])
  @@index([userId, createdAt(sort: Desc)])
  @@index([accountId, archived])   // For filtering archived mentions per account
  @@index([userId, archived])      // For filtering archived mentions per user
  @@map("mentions")
}

// AI-generated responses with enhanced tracking
model AIResponse {
  id           String       @id @default(cuid())
  
  // Content
  content      String       // AI-generated reply text
  model        String?      // Which AI model was used
  prompt       String?      // Original prompt used
  tokensUsed   Int?         // Token consumption tracking
  
  // Relationships
  mention      Mention      @relation(fields: [mentionId], references: [id], onDelete: Cascade)
  mentionId    String
  user         User?        @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId       String?
  images       Image[]      // Generated images
  
  // Quality metrics
  confidence   Float?       // AI confidence score (0.0-1.0)
  rating       Int?         // User rating (1-5 stars)
  used         Boolean      @default(false) // Whether user actually used this response
  
  // Processing metadata
  processingTime BigInt?    // Milliseconds to generate
  version      String?      // AI model version
  
  // Timestamps
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  // Indexes
  @@index([mentionId])
  @@index([userId])
  @@index([model])
  @@index([createdAt(sort: Desc)])
  @@index([rating(sort: Desc)])
  @@index([used])
  // Compound indexes
  @@index([userId, createdAt(sort: Desc)])
  @@index([mentionId, createdAt(sort: Desc)])
  @@map("ai_responses")
}

// Image storage with metadata
model Image {
  id           String      @id @default(cuid())
  
  // File information
  url          String      @unique // UploadThing file URL
  filename     String?     // Original filename
  fileSize     Int?        // Size in bytes
  mimeType     String?     // MIME type
  uploadKey    String?     // UploadThing file key for management
  
  // Image metadata
  width        Int?        // Image dimensions
  height       Int?
  altText      String?     // Accessibility description
  
  // Relationships
  aiResponse   AIResponse? @relation(fields: [aiResponseId], references: [id], onDelete: SetNull)
  aiResponseId String?
  mention      Mention?    @relation(fields: [mentionId], references: [id], onDelete: SetNull)
  mentionId    String?
  user         User?       @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId       String?
  
  // Status
  processed    Boolean     @default(false)
  isPublic     Boolean     @default(false)
  
  // Timestamps
  uploadedAt   DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  // Indexes
  @@index([aiResponseId])
  @@index([mentionId])
  @@index([userId])
  @@index([uploadedAt(sort: Desc)])
  @@index([mimeType])
  @@index([processed])
  @@map("images")
}

// Usage tracking for rate limiting
model UsageLog {
  id          String      @id @default(cuid())

  // Usage information
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      String
  feature     FeatureType // What feature was used
  amount      Int         @default(1) // How many units consumed

  // Metadata
  metadata    Json?       // Additional usage context

  // Billing period tracking
  billingPeriod String    // e.g. "2024-01" for monthly tracking

  // Timestamps
  createdAt   DateTime    @default(now())

  // Indexes for rate limiting queries
  @@index([userId, feature])
  @@index([userId, billingPeriod])
  @@index([createdAt(sort: Desc)])
  // Compound index for efficient rate limit checks
  @@index([userId, feature, billingPeriod])
  @@map("usage_logs")
}

// Mention Notepad for enhanced response crafting
model MentionNotepad {
  id          String      @id @default(cuid())

  // Relationships
  mention     Mention     @relation(fields: [mentionId], references: [id], onDelete: Cascade)
  mentionId   String      @unique // One notepad per mention
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      String

  // Notepad content
  title       String?     // Optional custom title
  notes       String?     // User's notes and thoughts
  draftResponse String?   // Current draft response
  finalResponse String?   // Final crafted response

  // Research session data
  researchQuery String?   // Last research query
  researchContext Json?   // Research session context

  // Status and metadata
  isActive    Boolean     @default(true)
  lastUsedAt  DateTime    @default(now())

  // Relationships
  sources     NotepadSource[]
  drafts      NotepadDraft[]

  // Timestamps
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Indexes
  @@index([mentionId])
  @@index([userId])
  @@index([lastUsedAt(sort: Desc)])
  @@index([isActive])
  @@map("mention_notepads")
}

// Sources and citations for notepad research
model NotepadSource {
  id          String      @id @default(cuid())

  // Relationships
  notepad     MentionNotepad @relation(fields: [notepadId], references: [id], onDelete: Cascade)
  notepadId   String

  // Source information
  title       String      // Source title
  url         String      // Source URL
  content     String?     // Extracted content/summary
  sourceType  String      // "web", "news", "research", "social"
  searchTool  String      // "xai", "exa", "manual"

  // Metadata
  relevanceScore Float?   // AI-assessed relevance (0.0-1.0)
  credibilityScore Float? // AI-assessed credibility (0.0-1.0)
  publishedAt DateTime?   // Source publication date
  extractedAt DateTime    @default(now()) // When we extracted this

  // User interaction
  isBookmarked Boolean    @default(false)
  userRating  Int?        // User rating 1-5
  userNotes   String?     // User's notes about this source

  // Timestamps
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Indexes
  @@index([notepadId])
  @@index([sourceType])
  @@index([searchTool])
  @@index([isBookmarked])
  @@index([relevanceScore(sort: Desc)])
  @@index([extractedAt(sort: Desc)])
  @@map("notepad_sources")
}

// Draft versions for response crafting
model NotepadDraft {
  id          String      @id @default(cuid())

  // Relationships
  notepad     MentionNotepad @relation(fields: [notepadId], references: [id], onDelete: Cascade)
  notepadId   String

  // Draft content
  content     String      // Draft response content
  version     Int         // Version number
  title       String?     // Optional draft title/label

  // Generation metadata
  generatedBy String?     // "user", "ai", "enhanced"
  model       String?     // AI model used if generated
  prompt      String?     // Prompt used for generation
  tokensUsed  Int?        // Tokens consumed

  // Status
  isActive    Boolean     @default(true)
  isFavorite  Boolean     @default(false)

  // Timestamps
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Indexes
  @@index([notepadId])
  @@index([version(sort: Desc)])
  @@index([isActive])
  @@index([isFavorite])
  @@index([createdAt(sort: Desc)])
  @@map("notepad_drafts")
}

// Crypto cache tables for performance optimization
model CryptoSectorsCache {
  id        Int      @id @default(autoincrement())
  data      Json     // JSONB data containing sectors information
  expiresAt DateTime @map("expires_at")
  createdAt DateTime? @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  @@map("crypto_sectors_cache")
}

model CryptoTrendingCache {
  id         Int      @id @default(autoincrement())
  sectorSlug String?  @map("sector_slug") @db.VarChar
  timeframe  String   @db.VarChar
  data       Json     // JSONB data containing trending projects
  expiresAt  DateTime @map("expires_at")
  createdAt  DateTime? @default(now()) @map("created_at")
  updatedAt  DateTime? @updatedAt @map("updated_at")

  // Indexes for efficient cache lookups
  @@index([sectorSlug, timeframe])
  @@index([expiresAt])
  @@index([createdAt(sort: Desc)])
  @@map("crypto_trending_cache")
}

// Memory model for Mem0 AI memory storage
model Memory {
  id                String   @id @default(cuid())

  // Vector embedding storage (handled as String in Prisma, actual vector in DB)
  embedding         String   // Will be stored as VECTOR(1536) in PostgreSQL

  // Memory content and metadata
  content           String
  metadata          Json     @default("{}")

  // User isolation - ensure each user has their own memory space
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId            String

  // Memory categorization
  memoryType        String   @default("conversation") // 'conversation', 'preference', 'fact', etc.

  // Relevance and decay management
  relevanceScore    Float    @default(1.0)
  accessCount       Int      @default(0)
  lastAccessedAt    DateTime @default(now())

  // Standard timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Indexes for optimal performance
  @@index([userId])
  @@index([memoryType])
  @@index([relevanceScore(sort: Desc)])
  @@index([createdAt(sort: Desc)])
  @@index([lastAccessedAt(sort: Desc)])
  @@index([userId, memoryType])
  @@index([userId, relevanceScore(sort: Desc)])
  @@map("memories")
}

// Telegram integration models
model TelegramUser {
  id           String   @id @default(cuid())

  // Telegram user information
  telegramId   String   @unique @map("telegram_id") // Telegram user ID (as string for large numbers)
  username     String?  // Telegram username (without @)
  firstName    String?  @map("first_name")
  lastName     String?  @map("last_name")
  languageCode String?  @map("language_code") // User's language preference

  // Link to BuddyChip user
  user         User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId       String?  @map("user_id")

  // Bot interaction settings
  isActive     Boolean  @default(true) @map("is_active")
  isBlocked    Boolean  @default(false) @map("is_blocked") // If user blocked the bot

  // Conversation state
  sessions     TelegramSession[]

  // Timestamps
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  lastActiveAt DateTime? @map("last_active_at")

  // Indexes
  @@index([telegramId])
  @@index([userId])
  @@index([username])
  @@index([isActive])
  @@index([lastActiveAt(sort: Desc)])
  @@map("telegram_users")
}

model TelegramSession {
  id           String   @id @default(cuid())

  // Session information
  telegramUser TelegramUser @relation(fields: [telegramUserId], references: [id], onDelete: Cascade)
  telegramUserId String   @map("telegram_user_id")

  // Conversation context
  context      Json?    // Conversation history and state
  currentTool  String?  @map("current_tool") // Active tool/command

  // Session metadata
  isActive     Boolean  @default(true) @map("is_active")
  expiresAt    DateTime @map("expires_at")

  // Timestamps
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Indexes
  @@index([telegramUserId])
  @@index([isActive])
  @@index([expiresAt])
  @@index([createdAt(sort: Desc)])
  @@map("telegram_sessions")
}
