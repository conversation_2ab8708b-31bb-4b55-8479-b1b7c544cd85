/**
 * Fix User Monitored Accounts Consistency
 *
 * Resolves data inconsistency where users get "limit reached" errors
 * but the UI shows 0 accounts. This happens when there are orphaned
 * or inactive records affecting the count.
 */

import { PrismaClient } from "../../prisma/generated/index.js";

const prisma = new PrismaClient();

async function fixUserMonitoredAccounts(userId?: string) {
  console.log("🔧 Starting monitored accounts consistency fix...");

  try {
    // If specific user provided, fix just that user
    if (userId) {
      await fixUserAccounts(userId);
    } else {
      // Fix all users with potential issues
      console.log("🔍 Finding users with account inconsistencies...");

      // Find users who might have inconsistent data
      const usersWithAccounts = await prisma.user.findMany({
        include: {
          monitoredAccounts: true,
        },
      });

      for (const user of usersWithAccounts) {
        const activeCount = user.monitoredAccounts.filter(
          (acc) => acc.isActive
        ).length;
        const totalCount = user.monitoredAccounts.length;

        if (activeCount > 0 || totalCount > 0) {
          console.log(
            `🔍 User ${user.id}: ${activeCount} active, ${totalCount} total accounts`
          );
          await fixUserAccounts(user.id);
        }
      }
    }

    console.log("✅ Monitored accounts consistency fix completed");
  } catch (error) {
    console.error("❌ Error during consistency fix:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function fixUserAccounts(userId: string) {
  console.log(`\n🔧 Fixing accounts for user: ${userId}`);

  // Get all accounts for this user
  const accounts = await prisma.monitoredAccount.findMany({
    where: { userId },
    include: {
      _count: {
        select: { mentions: true },
      },
    },
  });

  console.log(`📋 Found ${accounts.length} total accounts for user ${userId}`);

  // Show current state
  for (const account of accounts) {
    console.log(
      `  - ${account.twitterHandle}: isActive=${account.isActive}, mentions=${account._count.mentions}`
    );
  }

  // Clean up any duplicate accounts (same handle)
  const handleCounts = new Map<string, typeof accounts>();
  for (const account of accounts) {
    const existing = handleCounts.get(account.twitterHandle);
    if (existing) {
      // Keep the one with more mentions or newer creation date
      const keepAccount =
        account._count.mentions > existing[0]._count.mentions ||
        account.createdAt > existing[0].createdAt
          ? account
          : existing[0];
      const deleteAccount = keepAccount === account ? existing[0] : account;

      console.log(
        `🗑️ Removing duplicate account: ${deleteAccount.twitterHandle} (${deleteAccount.id})`
      );
      await prisma.monitoredAccount.delete({
        where: { id: deleteAccount.id },
      });
    } else {
      handleCounts.set(account.twitterHandle, [account]);
    }
  }

  // Get updated accounts after cleanup
  const cleanedAccounts = await prisma.monitoredAccount.findMany({
    where: { userId },
  });

  // Ensure user's plan limit is respected
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      plan: {
        include: { features: true },
      },
    },
  });

  const monitoredAccountsFeature = user?.plan.features.find(
    (f) => f.feature === "MONITORED_ACCOUNTS"
  );
  const limit = monitoredAccountsFeature?.limit || 1;

  console.log(`📊 User's monitored accounts limit: ${limit}`);

  if (cleanedAccounts.length > limit) {
    console.log(
      `⚠️ User has ${cleanedAccounts.length} accounts but limit is ${limit}`
    );

    // Sort by creation date and keep the newest ones
    const sortedAccounts = cleanedAccounts.sort(
      (a, b) => b.createdAt.getTime() - a.createdAt.getTime()
    );
    const accountsToKeep = sortedAccounts.slice(0, limit);
    const accountsToRemove = sortedAccounts.slice(limit);

    console.log(
      `🔧 Keeping ${accountsToKeep.length} newest accounts, removing ${accountsToRemove.length}`
    );

    for (const account of accountsToRemove) {
      console.log(`🗑️ Removing excess account: ${account.twitterHandle}`);
      await prisma.monitoredAccount.delete({
        where: { id: account.id },
      });
    }
  }

  // Final state check
  const finalAccounts = await prisma.monitoredAccount.findMany({
    where: { userId },
  });

  const activeCount = finalAccounts.filter((acc) => acc.isActive).length;
  const totalCount = finalAccounts.length;

  console.log(
    `✅ Final state for user ${userId}: ${activeCount} active, ${totalCount} total accounts`
  );

  return {
    userId,
    before: accounts.length,
    after: finalAccounts.length,
    activeCount,
    limit,
  };
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const userId = process.argv[2]; // Optional user ID argument
  fixUserMonitoredAccounts(userId)
    .then(() => process.exit(0))
    .catch((error) => {
      console.error("Script failed:", error);
      process.exit(1);
    });
}

export { fixUserMonitoredAccounts };
